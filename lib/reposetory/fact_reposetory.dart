import 'dart:convert';

import 'package:dep_inj_test/domain/models/fact.dart' show Fact;
import 'package:http/http.dart' as http;

class FactReposetory {
  Future<Fact> getFact() async {
    final response = await http.get(Uri.parse('https://catfact.ninja/fact'));
    if (response.statusCode == 200) {
      return Fact.fromJson(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load fact');
    }
  }
}
