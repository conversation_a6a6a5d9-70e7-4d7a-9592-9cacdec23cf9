import 'package:dep_inj_test/domain/fact_controller.dart';
import 'package:dep_inj_test/domain/models/fact.dart';
import 'package:dep_inj_test/locator.dart';
import 'package:flutter/material.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  late Fact visibleFact;
  late Future<Fact> _futureFact;

  @override
  void initState() {
    super.initState();
    _futureFact = locator.get<FactController>().getNextFact();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Home Page')),
      body: Center(
        child: Column(
          children: [
            Text(visibleFact.fact),
            ElevatedButton(
              onPressed: () async {
                Fact fact = await locator.get<FactController>().getNextFact();

                setState(() {
                  visibleFact = fact;
                });
              },
              child: const Text('get next fact'),
            ),
          ],
        ),
      ),
    );
  }
}
